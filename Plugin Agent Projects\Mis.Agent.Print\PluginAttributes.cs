using System;

namespace Mis.Agent.Print
{
    /// <summary>
    /// Marks a class as a plugin that should be loaded by the plugin system
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class PluginAttribute : Attribute
    {
        /// <summary>
        /// The display name of the plugin (used for tab title)
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// The version of the plugin
        /// </summary>
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// Description of what the plugin does
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// The order in which this plugin should appear in tabs (lower numbers appear first)
        /// </summary>
        public int Order { get; set; } = 100;

        /// <summary>
        /// Whether this plugin should be loaded automatically
        /// </summary>
        public bool AutoLoad { get; set; } = true;

        public PluginAttribute(string name)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
        }
    }

    /// <summary>
    /// Marks a method as the plugin's tab page provider
    /// Method must return a TabPage or UserControl
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class PluginTabProviderAttribute : Attribute
    {
    }

    /// <summary>
    /// Marks a method as the plugin's initialization method
    /// Method will be called when the plugin is loaded
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class PluginInitializeAttribute : Attribute
    {
    }

    /// <summary>
    /// Marks a method as the plugin's cleanup method
    /// Method will be called when the plugin is unloaded
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class PluginCleanupAttribute : Attribute
    {
    }

    /// <summary>
    /// Marks a method as a configuration provider
    /// Method should return configuration values
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public class PluginConfigurationAttribute : Attribute
    {
        /// <summary>
        /// The configuration key this method provides
        /// </summary>
        public string Key { get; }

        public PluginConfigurationAttribute(string key)
        {
            Key = key ?? throw new ArgumentNullException(nameof(key));
        }
    }

    /// <summary>
    /// Marks a method as a notification handler
    /// Method will be called to show notifications
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class PluginNotificationHandlerAttribute : Attribute
    {
    }
}
