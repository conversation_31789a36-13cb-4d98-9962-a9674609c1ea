﻿
namespace Mis.Agent.Barcode
{
    partial class BarcodeForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl1 = new TabControl();
            BarcodeTab = new TabPage();
            barcodeProtocolField = new ComboBox();
            label3 = new Label();
            barcodeIpField = new TextBox();
            label2 = new Label();
            barcodePortField = new TextBox();
            label1 = new Label();
            button3 = new Button();
            label6 = new Label();
            comboBoxCOMPorts = new ComboBox();
            label5 = new Label();
            comPortTextBox = new TextBox();
            barcodeUrlTextBox = new TextBox();
            label4 = new Label();
            tabControl1.SuspendLayout();
            BarcodeTab.SuspendLayout();
            SuspendLayout();
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(BarcodeTab);
            tabControl1.Location = new Point(0, 0);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(1015, 630);
            tabControl1.TabIndex = 0;
            // 
            // BarcodeTab
            // 
            BarcodeTab.Controls.Add(barcodeProtocolField);
            BarcodeTab.Controls.Add(label3);
            BarcodeTab.Controls.Add(barcodeIpField);
            BarcodeTab.Controls.Add(label2);
            BarcodeTab.Controls.Add(barcodePortField);
            BarcodeTab.Controls.Add(label1);
            BarcodeTab.Controls.Add(button3);
            BarcodeTab.Controls.Add(label6);
            BarcodeTab.Controls.Add(comboBoxCOMPorts);
            BarcodeTab.Controls.Add(label5);
            BarcodeTab.Controls.Add(comPortTextBox);
            BarcodeTab.Controls.Add(barcodeUrlTextBox);
            BarcodeTab.Controls.Add(label4);
            BarcodeTab.Location = new Point(4, 29);
            BarcodeTab.Name = "BarcodeTab";
            BarcodeTab.Padding = new Padding(3);
            BarcodeTab.Size = new Size(1007, 597);
            BarcodeTab.TabIndex = 0;
            BarcodeTab.Text = "Barcode Tab";
            BarcodeTab.UseVisualStyleBackColor = true;
            // 
            // barcodeProtocolField
            // 
            barcodeProtocolField.DropDownStyle = ComboBoxStyle.DropDownList;
            barcodeProtocolField.Enabled = false;
            barcodeProtocolField.Items.AddRange(new object[] { "http", "https" });
            barcodeProtocolField.Location = new Point(677, 216);
            barcodeProtocolField.Name = "barcodeProtocolField";
            barcodeProtocolField.Size = new Size(147, 28);
            barcodeProtocolField.TabIndex = 25;
            barcodeProtocolField.SelectedIndexChanged += barcodeProtocolField_SelectedIndexChanged;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(848, 216);
            label3.Name = "label3";
            label3.Size = new Size(125, 20);
            label3.TabIndex = 24;
            label3.Text = " بروتوكول الباركود ";
            // 
            // barcodeIpField
            // 
            barcodeIpField.Location = new Point(23, 219);
            barcodeIpField.Name = "barcodeIpField";
            barcodeIpField.Size = new Size(179, 27);
            barcodeIpField.TabIndex = 23;
            barcodeIpField.TextChanged += barcodeIpField_TextChanged;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(232, 222);
            label2.Name = "label2";
            label2.Size = new Size(81, 20);
            label2.TabIndex = 22;
            label2.Text = " الباركود  Ip";
            // 
            // barcodePortField
            // 
            barcodePortField.Location = new Point(357, 219);
            barcodePortField.Name = "barcodePortField";
            barcodePortField.Size = new Size(125, 27);
            barcodePortField.TabIndex = 21;
            barcodePortField.TextChanged += barcodePortField_TextChanged;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(506, 219);
            label1.Name = "label1";
            label1.Size = new Size(130, 20);
            label1.TabIndex = 20;
            label1.Text = "منفذ عنوان الباركود";
            // 
            // button3
            // 
            button3.Location = new Point(-227, 233);
            button3.Name = "button3";
            button3.Size = new Size(94, 37);
            button3.TabIndex = 19;
            button3.Text = "button3";
            button3.UseVisualStyleBackColor = true;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(299, 84);
            label6.Name = "label6";
            label6.Size = new Size(142, 20);
            label6.TabIndex = 18;
            label6.Text = "المنفذ الموصول حالياً";
            // 
            // comboBoxCOMPorts
            // 
            comboBoxCOMPorts.FormattingEnabled = true;
            comboBoxCOMPorts.Location = new Point(506, 80);
            comboBoxCOMPorts.Name = "comboBoxCOMPorts";
            comboBoxCOMPorts.Size = new Size(146, 28);
            comboBoxCOMPorts.TabIndex = 17;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(676, 83);
            label5.Name = "label5";
            label5.Size = new Size(160, 20);
            label5.TabIndex = 16;
            label5.Text = "المنافذ المتوفرة للاتصال";
            // 
            // comPortTextBox
            // 
            comPortTextBox.Location = new Point(106, 81);
            comPortTextBox.Name = "comPortTextBox";
            comPortTextBox.Size = new Size(146, 27);
            comPortTextBox.TabIndex = 15;
            // 
            // barcodeUrlTextBox
            // 
            barcodeUrlTextBox.BackColor = Color.Gray;
            barcodeUrlTextBox.Location = new Point(256, 326);
            barcodeUrlTextBox.Name = "barcodeUrlTextBox";
            barcodeUrlTextBox.PlaceholderText = "http://localhost:4000/chatHub";
            barcodeUrlTextBox.ReadOnly = true;
            barcodeUrlTextBox.Size = new Size(278, 27);
            barcodeUrlTextBox.TabIndex = 14;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(629, 329);
            label4.Name = "label4";
            label4.Size = new Size(96, 20);
            label4.TabIndex = 13;
            label4.Text = "عنوان الباركود";
            // 
            // BarcodeForm
            // 
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1018, 628);
            Controls.Add(tabControl1);
            Name = "BarcodeForm";
            Text = "Barcode Form";
            tabControl1.ResumeLayout(false);
            BarcodeTab.ResumeLayout(false);
            BarcodeTab.PerformLayout();
            ResumeLayout(false);
        }



        #endregion

        private TabControl tabControl1;
        public TabPage BarcodeTab;
        private Button button3;
        private Label label6;
        public ComboBox comboBoxCOMPorts;
        private Label label5;
        public TextBox comPortTextBox;
        public TextBox barcodeUrlTextBox;
        private Label label4;
        private TextBox barcodePortField;
        private Label label1;
        private Label label2;
        private TextBox barcodeIpField;
        ComboBox barcodeProtocolField ;
        private Label label3;
        // private Button SaveBarcodeConfiguration;
    }
}
