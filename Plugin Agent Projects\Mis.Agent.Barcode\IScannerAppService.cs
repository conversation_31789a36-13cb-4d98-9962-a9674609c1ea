﻿using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    public interface IScannerAppService
    {
        string GetSetting(string key);
        List<string> GetAvailableScanners();
        void ShowNotification(string title, string text);
        string GetCOMPort();
        string GetBarcodeBaseUrl();
        string ScanWithSelectedScanner(string scannerName);
    }
}
