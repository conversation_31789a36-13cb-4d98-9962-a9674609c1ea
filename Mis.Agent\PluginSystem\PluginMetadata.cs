using System;
using System.Reflection;
using System.Windows.Forms;

namespace Mis.Agent.PluginSystem
{
    /// <summary>
    /// Contains metadata and runtime information about a loaded plugin
    /// </summary>
    public class PluginMetadata
    {
        /// <summary>
        /// The plugin's display name
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// The plugin's version
        /// </summary>
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// The plugin's description
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// The order in which this plugin should appear
        /// </summary>
        public int Order { get; set; } = 100;

        /// <summary>
        /// Whether this plugin should be auto-loaded
        /// </summary>
        public bool AutoLoad { get; set; } = true;

        /// <summary>
        /// The plugin's main class type
        /// </summary>
        public Type PluginType { get; set; } = null!;

        /// <summary>
        /// The assembly containing the plugin
        /// </summary>
        public Assembly Assembly { get; set; } = null!;

        /// <summary>
        /// The file path of the plugin assembly
        /// </summary>
        public string AssemblyPath { get; set; } = "";

        /// <summary>
        /// The instantiated plugin object
        /// </summary>
        public object? PluginInstance { get; set; }

        /// <summary>
        /// Whether the plugin is currently loaded
        /// </summary>
        public bool IsLoaded { get; set; }

        /// <summary>
        /// The method that provides the tab page
        /// </summary>
        public MethodInfo? TabProviderMethod { get; set; }

        /// <summary>
        /// The method that initializes the plugin
        /// </summary>
        public MethodInfo? InitializeMethod { get; set; }

        /// <summary>
        /// The method that cleans up the plugin
        /// </summary>
        public MethodInfo? CleanupMethod { get; set; }

        /// <summary>
        /// The method that handles notifications
        /// </summary>
        public MethodInfo? NotificationHandlerMethod { get; set; }

        /// <summary>
        /// Configuration methods mapped by their keys
        /// </summary>
        public Dictionary<string, MethodInfo> ConfigurationMethods { get; set; } = new();

        /// <summary>
        /// The tab page created by this plugin (if any)
        /// </summary>
        public TabPage? TabPage { get; set; }

        /// <summary>
        /// Any error that occurred during plugin loading
        /// </summary>
        public Exception? LoadError { get; set; }

        /// <summary>
        /// When the plugin was loaded
        /// </summary>
        public DateTime LoadedAt { get; set; }

        /// <summary>
        /// Gets a configuration value from the plugin
        /// </summary>
        public object? GetConfiguration(string key)
        {
            if (PluginInstance == null || !ConfigurationMethods.TryGetValue(key, out var method))
                return null;

            try
            {
                return method.Invoke(PluginInstance, null);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting configuration '{key}' from plugin '{Name}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Shows a notification using the plugin's notification handler
        /// </summary>
        public void ShowNotification(string title, string text)
        {
            if (PluginInstance == null || NotificationHandlerMethod == null)
                return;

            try
            {
                NotificationHandlerMethod.Invoke(PluginInstance, new object[] { title, text });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error showing notification in plugin '{Name}': {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the tab page from the plugin
        /// </summary>
        public TabPage? GetTabPage()
        {
            Console.WriteLine($"Getting tab page for plugin: {Name}");
            Console.WriteLine($"PluginInstance is null: {PluginInstance == null}");
            Console.WriteLine($"TabProviderMethod is null: {TabProviderMethod == null}");

            if (PluginInstance == null || TabProviderMethod == null)
                return null;

            try
            {
                var result = TabProviderMethod.Invoke(PluginInstance, null);

                if (result is TabPage tabPage)
                {
                    TabPage = tabPage;
                    return tabPage;
                }
                else if (result is UserControl userControl)
                {
                    // Wrap UserControl in a TabPage
                    TabPage = new TabPage(Name)
                    {
                        Controls = { userControl }
                    };
                    userControl.Dock = DockStyle.Fill;
                    return TabPage;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting tab page from plugin '{Name}': {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Initializes the plugin
        /// </summary>
        public bool Initialize()
        {
            if (PluginInstance == null || InitializeMethod == null)
                return true; // No initialization method is OK

            try
            {
                InitializeMethod.Invoke(PluginInstance, null);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing plugin '{Name}': {ex.Message}");
                LoadError = ex;
                return false;
            }
        }

        /// <summary>
        /// Cleans up the plugin
        /// </summary>
        public void Cleanup()
        {
            if (PluginInstance == null || CleanupMethod == null)
                return;

            try
            {
                CleanupMethod.Invoke(PluginInstance, null);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error cleaning up plugin '{Name}': {ex.Message}");
            }
        }
    }
}
