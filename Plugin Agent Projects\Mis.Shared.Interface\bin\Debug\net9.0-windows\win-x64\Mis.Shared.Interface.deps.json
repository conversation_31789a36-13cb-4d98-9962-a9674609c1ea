{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"Mis.Shared.Interface/1.0.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Client": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.NET.ILLink.Tasks": "9.0.6", "System.Data.SQLite": "1.0.119", "System.Drawing.Common": "9.0.6", "System.IO.Ports": "9.0.6", "Interop.WIA": "*******", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "9.0.6", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "9.0.6"}, "runtime": {"Mis.Shared.Interface.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.6": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "14.0.625.26613"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "9.0.625.26613"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "9.0.625.26613"}, "clretwrc.dll": {"fileVersion": "9.0.625.26613"}, "clrgc.dll": {"fileVersion": "9.0.625.26613"}, "clrgcexp.dll": {"fileVersion": "9.0.625.26613"}, "clrjit.dll": {"fileVersion": "9.0.625.26613"}, "coreclr.dll": {"fileVersion": "9.0.625.26613"}, "createdump.exe": {"fileVersion": "9.0.625.26613"}, "hostfxr.dll": {"fileVersion": "9.0.625.26613"}, "hostpolicy.dll": {"fileVersion": "9.0.625.26613"}, "mscordaccore.dll": {"fileVersion": "9.0.625.26613"}, "mscordaccore_amd64_amd64_9.0.625.26613.dll": {"fileVersion": "9.0.625.26613"}, "mscordbi.dll": {"fileVersion": "9.0.625.26613"}, "mscorrc.dll": {"fileVersion": "9.0.625.26613"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.6": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "9.0.625.26605"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Formats.Nrbf.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}}}, "EntityFramework/6.4.4": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.CodeDom": "4.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.SqlClient": "4.8.1"}, "runtime": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}, "lib/netstandard2.1/EntityFramework.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}}}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Features": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Net.ServerSentEvents": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Client/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "9.0.6", "Microsoft.AspNetCore.SignalR.Client.Core": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Client.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.6": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.6", "Microsoft.AspNetCore.SignalR.Protocols.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "System.Threading.Channels": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Common/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.6": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Features/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.NET.ILLink.Tasks/9.0.6": {}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Win32.SystemEvents/9.0.6": {"runtime": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/9.0.6": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.6"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"native": {"runtimes/win-x64/native/sni.dll": {"fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "native": {"runtimes/win-x64/native/SQLite.Interop.dll": {"fileVersion": "*********"}}}, "System.CodeDom/4.7.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.ComponentModel.Annotations/4.7.0": {"runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}}, "System.Data.SQLite/1.0.119": {"dependencies": {"System.Data.SQLite.Core": "1.0.119", "System.Data.SQLite.EF6": "1.0.119"}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Data.SQLite.EF6/1.0.119": {"dependencies": {"EntityFramework": "6.4.4"}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.EF6.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.Drawing.Common/9.0.6": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.6"}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "lib/net9.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}}}, "System.IO.Ports/9.0.6": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.6"}, "runtime": {"runtimes/win/lib/net9.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Net.ServerSentEvents/9.0.6": {"runtime": {"lib/net9.0/System.Net.ServerSentEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Principal.Windows/4.7.0": {"runtime": {"runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Threading.Channels/9.0.6": {"runtime": {"lib/net9.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "9.0.6"}, "runtime": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Interop.WIA/*******": {"runtime": {"Interop.WIA.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Mis.Shared.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.6": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.6": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "EntityFramework/6.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "path": "entityframework/6.4.4", "hashPath": "entityframework.6.4.4.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-78aCqAAdbJvwRMyGp63iVcSobLb+LrZ8EjIU06dXwflBSpUDkicZPppJuoggdvWvc1W8eA62w5Sh7+ZFZvsutg==", "path": "microsoft.aspnetcore.connections.abstractions/9.0.6", "hashPath": "microsoft.aspnetcore.connections.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-fzJfVBPaeLliimP9lyzhE5el7a2eosCLk0saQlkjJH1368Q6belE2nI+thpZ/JZ9koEm0IQPP+6/EwjK/v0I6w==", "path": "microsoft.aspnetcore.http.connections.client/9.0.6", "hashPath": "microsoft.aspnetcore.http.connections.client.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BPXsi0m4L31mZy9HHDrDN5gGjvJkRKktB7ZjHIuX6Cha7WYadGYHGaz9LmUcswCGb00MEJX1aSc0wR//wr56cQ==", "path": "microsoft.aspnetcore.http.connections.common/9.0.6", "hashPath": "microsoft.aspnetcore.http.connections.common.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VX8yakZUQT1+vtTIKQxuFijzS9ET5vmWpSv7uvxb0AoVIb+8KL99FsbJBG7gq4TlCsss9DPEoat42t40yTzM2A==", "path": "microsoft.aspnetcore.signalr.client/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.client.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-sXCPxeECWqVIxfzH4nlV0m17R/IelbJ8B+W1e+E3Ne/wqVUphd+b3MbaSGficNiahRnX1UwKUxT4dAFFilJtnA==", "path": "microsoft.aspnetcore.signalr.client.core/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.client.core.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-sZDm6Nru8wBMOb6Bq9qlTiK5H/8yL7FY08bXydOncJSpVfbj2TwmqjUCR4AmJY6Dvxdhf8f29VgikBSz2EYoiw==", "path": "microsoft.aspnetcore.signalr.common/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.common.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pRCQi9Nu60gyOvGzPXJyP5faSvE4FbDFevnv7/deviodbHULDyqJ82aYhCV98f8AyUh0Yhu98EovIyC9YkdFYg==", "path": "microsoft.aspnetcore.signalr.protocols.json/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.9.0.6.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Features/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q3VizRxWXP185iqK4bFFsADAuLNaJX6IUsc3lE8/efc5xp1Wu7iYPgJPFUj2Ur4X0d7gfeHkprtY72ThmwA05Q==", "path": "microsoft.extensions.features/9.0.6", "hashPath": "microsoft.extensions.features.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-TXy3SbJzGXQbxxIxCjdrp8bwEyTDImyYNpTpd6v7P3JL2Y7dno8EYG7dPezfYTa5SoWKdhbH9cbnwHHs3BR5gA==", "path": "microsoft.net.illink.tasks/9.0.6", "hashPath": "microsoft.net.illink.tasks.9.0.6.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-mUa3Chovao0xQ74j2hUN5kDTn3luY/1e9usJYT0r51cgqfQsgQu5C8Cis9h65SNRbkbBwErfXKlBtHCkMiYa/g==", "path": "microsoft.win32.systemevents/9.0.6", "hashPath": "microsoft.win32.systemevents.9.0.6.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-S1Gu7KIiBM5Zfkve5iaVZUMPToGZ8fc13IMLNdyU20G9nq9LnKSN5e0xb/TFr4N6IqWkAxmTD4JkcWXdjdk33g==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-B5GL3MQcQ4OI02Q3jXdiISS5N7ZI6LCHyDQTpfJTpzTdf4SDwTMMxrcpGaPSth6l7yVyAtJJbIhgdFDw3PmOhg==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7Aq/6BqdFCShOlL+f7ltFCQkwo/YFzZ+wOmK8ObpGfzhxWp2Mg7H4DuMoqd1pO+ikdfbOcDm7cfdMmsUwgbKkQ==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LG0iGAvkdSWD3WT+oZcozJFZKeINQ160n68YfnwVgtmFpMR2O3GIfuMrf9WJjfnZJb6pbaNnLGqOTpXVJTJa2Q==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-RzAA2D77LRuYVTUXKG8wLRbbDF6BA+fHeBtsdar3ARj3cWmqscR3sb5QgROBKtDj1G2Idu3aj+5Bk3TYc+f4XA==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a9pVVDsO76cdoGN3CYEgxTE54C5udq9hfOjPKVxyfwOk1N12w18VKL2a15deezFMqjGggaVyK0cFBj9qG7pqWw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Kb8CZNgH0aWd8Ks6jS1F286SmVJ4FmJjiUdrQTvHu1aN1cWpfwLZ1qOARvFI3lbXE/geOzBIHDNWmQjyOAeUlg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BCbWsWzSdv9ly9pR1JFY89an+8nc9yZtjAPPlcpH31UUP0AuI27rnrKzcZAuqFXlKy2M8EZVnjV0czSTFueqGA==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-eapJ2k/eeMK7Q/Mbticy5rgSb+qI884W9Wk6UTDnEdRpd0BvKbhgM845QEmk3vrxT6B8cCr4A8pRseZBdmk4WA==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-+3S2ksd6iFNeyAxtCZ2dGlxXNGQsOgIgGiecu34++UnUTY9KFhkg8T69hyjEMg4+dRQXEWrU4+vP4AI3s5GlMw==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Fa+EbKuQ6W4EzdVRAA/6ffJ3C0eQ93D8bhmnFaVEHBkfDTKNUSZKhjLdYgubvMrSQlsQ8XLGw0Ld1UXMgGCj7w==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-POgI6/WVHtpxbqvfFqVybZckRbgFVp3nE0fOBpIQdKiZ9C3MPKKibyFNEBK81ZlgmtTEpJP0jMvLSuEbA/p95g==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VMJ5KlYwc2OUPEXmuMetGdwU2IKCDH0mYPz+7G0e2psKJ6Q4JVq9VIOK/nnFJ9z0nbw7Cxu5m7nwh8p/ZPr/eA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-kbPhYFOoSo7l71lZo4RFx51Vj6BbQAL3QFn4duULbrpV1GEQX5ZrmBSpdxigcvDMit1i/+wDTyMll9t56i/knQ==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Q8gHRVpOlTQSEqKtimf7s3lr8OaYaxXcrXjtF78k+6RogQ0BpEHnUgeBZBoQ53qSiztBAzkF22uPOHq+/+goOA==", "path": "runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a+KcHUMoNJG1Z9uvf28HDmT8jK5rm0ExTUdScsdP/ukU2KE7ah+vLxNbh4zCxzvGHsx+Z6bVpaWLjuSYNbqilQ==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ogySJ/MVq9J/X1ugQOAA4dQfoJSUnZtIbPLr8t2tsaGkV7TBgWOnFInRXy1c20o79M6ARyus12UinDKsFaLkwA==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "System.CodeDom/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hs9pw/kmvH3lXaZ1LFKj3pLQsiGfj2xo3sxSzwiLlRL6UcMZUTeCfoJ9Udalvn3yq5dLlPEZzYegrTQ1/LhPOQ==", "path": "system.codedom/4.7.0", "hashPath": "system.codedom.4.7.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Data.SQLite/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-JSOJpnBf9goMnxGTJFGCmm6AffxgtpuXNXV5YvWO8UNC2zwd12qkUe5lAbnY+2ohIkIukgIjbvR1RA/sWILv3w==", "path": "system.data.sqlite/1.0.119", "hashPath": "system.data.sqlite.1.0.119.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-BwwgCSeA80gsxdXtU7IQEBrN9kQXWQrD11hNYOJZbXBBI1C4r7hA4QhBAalO1nzijXikthGRUADIEMI3nlucLA==", "path": "system.data.sqlite.ef6/1.0.119", "hashPath": "system.data.sqlite.ef6.1.0.119.nupkg.sha512"}, "System.Drawing.Common/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-KpPB6e7PYCalslhmq/dVa2tJgCcfUDhEK83j1Eix+BmEcNPP00JJBvFrBT7jGcHLmQzsIA4AthwNFkk+cD3RKA==", "path": "system.drawing.common/9.0.6", "hashPath": "system.drawing.common.9.0.6.nupkg.sha512"}, "System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-D1nZmsZfUfKQ9/AqiAEafmnYijUoJvXtl0RWZ2P+q/Wq3gXgEtp+NzKTpabw2s0aiuPAsdx8SujQY06W2X4ucQ==", "path": "system.io.ports/9.0.6", "hashPath": "system.io.ports.9.0.6.nupkg.sha512"}, "System.Net.ServerSentEvents/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-gLRGZulz4D11qb9tGKlgxdSIQ8kB//x7u1nPJ8NaDyf1MZM+nLbzefXOWR9MUR+yGT/eI2iYTyj6pxwvQ5AzeQ==", "path": "system.net.serversentevents/9.0.6", "hashPath": "system.net.serversentevents.9.0.6.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Threading.Channels/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2MaNJVkG2yJiXQbZrrcYoJ55ehV+aX0zqR6rWJkO/Qj7jTsArWthrQ7iWywUf/sE5ylJWX/iLH2kKfwSRdkWsA==", "path": "system.threading.channels/9.0.6", "hashPath": "system.threading.channels.9.0.6.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "Interop.WIA/*******": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"]}}