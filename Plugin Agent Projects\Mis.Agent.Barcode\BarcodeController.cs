﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mis.Agent.Barcode.Resources;
using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Mis.Agent.Barcode
{
    [ApiController]
    [Route("[controller]")]
    [EnableCors("AllowAll")]
    public class BarcodeController : ControllerBase
    {
        private readonly IBarcodeAppService _barcodeAppService;
        private ILogger<BarcodeController> _logger;
        private readonly IScannerAppService _scannerAppService;
        public BarcodeController(IBarcodeAppService barcodeAppService,
            IScannerAppService scannerAppService, ILogger<BarcodeController> logger)
        {
            _barcodeAppService = barcodeAppService;
            _scannerAppService = scannerAppService;
            _logger = logger;
        }

        [HttpGet("ScanAsync")]
        public async Task<IActionResult> ScanAsync()
        {
            try
            {
                _logger.LogInformation("Enter method api ScanAsync");
                var setting = _scannerAppService.GetSetting("IsScanByBarcodeReader");
                bool.TryParse(setting, out bool isScanByBarcodeReader);

                if (isScanByBarcodeReader)
                    return await ScanUsingBarcodeReader();
                else
                    return ScanUsingScannerDevice();
            }
            catch (UnauthorizedAccessException)
            {
                _scannerAppService.ShowNotification(BarcodeResources.ErrorDuringScanning,BarcodeResources.AccessDeniedToTheCOMPort);
                _logger.LogError("Access denied to the COM port.");
                return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
            }
            catch (IOException ex)
            {
                _scannerAppService.ShowNotification(BarcodeResources.ErrorDuringScanning, ex.Message);
                _logger.LogError(ex.Message);

                return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
            }
            catch (Exception ex)
            {
                _scannerAppService.ShowNotification(BarcodeResources.ErrorDuringScanning, ex.Message);
                _logger.LogError(ex.Message);

                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        private async Task<IActionResult> ScanUsingBarcodeReader()
        {
            _logger.LogInformation("Enter Scan Using Barcode Reader");

            string comPort = _scannerAppService.GetCOMPort();
            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();

            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

            await _barcodeAppService.CaptureImageAsync(comPort);
            Thread.Sleep(2000);

            string scannedData = _barcodeAppService.LastScannedBase64Data;
            _barcodeAppService.LastScannedBase64Data = null;

            if (string.IsNullOrEmpty(scannedData))
            {
                NotificationManager.ShowNotification("BarcodeError", "ScanningFailedMessage", "Barcode Error", "Scanning failed. No image data received.");
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
            return Ok(new { success = true, data = scannedData });
        }

        private IActionResult ScanUsingScannerDevice()
        {
            _logger.LogInformation("Scan Using Scanner Device");

            string selectedScanner = _scannerAppService.GetSetting("Scanner");

            if (string.IsNullOrEmpty(selectedScanner))
            {
                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
            }

            var scanners = _scannerAppService.GetAvailableScanners();
            if (scanners == null || !scanners.Any())
            {
                return BadRequest(new { success = false, message = "No scanners found." });
            }

            string scannedData = _scannerAppService.ScanWithSelectedScanner(selectedScanner);

            if (string.IsNullOrEmpty(scannedData))
            {
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            return Ok(new { success = true, data = scannedData });
        }


        //[HttpGet("ScanAsync")]
        //public async Task<IActionResult> ScanAsync()
        //{
        //    try
        //    {
        //        //Retrieve the value of IsScanByBarcodeReader from the configuration(appsettings.json)
        //        bool isScanByBarcodeReader = bool.Parse(_scannerAppService.GetSetting("IsScanByBarcodeReader"));
        //        if (isScanByBarcodeReader)
        //        {
        //            //Barcode Reader logic
        //            string comPort = _scannerAppService.GetCOMPort();
        //            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
        //            // Initialize the barcode service
        //            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);
        //            // Capture the image
        //           await SerialPortManager.Instance.CaptureImageAsync(comPort);
        //             Thread.Sleep(2000);

        //            string scannedData = SerialPortManager.Instance.LastScannedBase64Data;

        //            SerialPortManager.Instance.LastScannedBase64Data = null;
        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        else
        //        {
        //            //Non - barcode scanner logic: Get the scanner name from appsettings

        //            string selectedScanner = _scannerAppService.GetSetting("Scanner");

        //            if (string.IsNullOrEmpty(selectedScanner))
        //            {
        //                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
        //            }

        //            //Scanner logic
        //            var scanners = _scannerAppService.GetAvailableScanners();
        //            if (scanners == null || !scanners.Any())
        //            {
        //                return BadRequest(new { success = false, message = "No scanners found." });
        //            }

        //            //Use the selected scanner to perform scanning
        //            string scannedData = SerialPortManager.Instance.ScanWithSelectedScanner(selectedScanner);

        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        return Ok(new { success = true });
        //    }
        //    catch (UnauthorizedAccessException ex)
        //    {
        //        return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
        //    }
        //    catch (IOException ex)
        //    {
        //        return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, new { success = false, message = ex.Message });
        //    }
        //}
    }
}
