using System.Drawing.Printing;
using System.Runtime.InteropServices;
using Mis.Shared.Interface;
using Mis.Agent.Print.Resources;

namespace Mis.Agent.Print
{
    public partial class PrintForm : Form, ICultureChangeNotifiable
    {
        private IPrintAppService _printAppService { get; set; }
        private string _previousPrinter;

        public PrintForm(IPrintAppService printAppService)
        {
            _printAppService = printAppService;
            InitializeComponent();
            InitializeLocalization();
            LoadPrinterSettings();
        }

        private void InitializeLocalization()
        {
            // Register for culture change notifications
            NotificationManager.RegisterForCultureChange(this);

            // Apply initial localization
            ApplyLocalization();
        }

        public void OnCultureChanged()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged()));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = PrintResources.PrintFormTitle;

            // Update tab text
            PrintTab.Text = PrintResources.PrintTabText;

            // Update labels
            printerLabel.Text = PrintResources.DefaultPrinterLabel;
            paperSizeLabel.Text = PrintResources.PaperSizeLabel;
            label1.Text = PrintResources.PrinterNameLabel;
            label2.Text = PrintResources.PaperType;

            // Update buttons
            button2.Text = PrintResources.Properties;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Unregister from culture change notifications
            NotificationManager.UnregisterFromCultureChange(this);
            base.OnFormClosing(e);
        }

        private void LoadPrinterSettings()
        {
            PrinterEntity printerSettings = _printAppService.GetPrinterEntity();
            textBox1.Text = printerSettings.DefaultPrinter;
            textBox2.Text = printerSettings.DefaultPaperSize;
            PopulatePrinterList();
        }
        public void PopulateForm()
        {
            LoadPrinterSettings();  // Load settings or populate other fields as needed
            PopulatePrinterList();  // Populate printer and paper size lists

            string selectedPrinter = printerComboBox?.SelectedItem?.ToString();
            string selectedPaperSize = paperSizeComboBox?.SelectedItem?.ToString();

            if (selectedPrinter != textBox1.Text || selectedPaperSize != textBox2.Text)
            {
                _printAppService.ShowNotification(PrintResources.PrintError, PrintResources.PleaseClickSaveToUsePrinterConnected);
            }
        }
        private void PopulatePrinterList()
        {
            printerComboBox.Items.Clear();
            foreach (string printer in _printAppService.GetInstalledPrinters())
            {
                printerComboBox.Items.Add(printer);
            }

            if (printerComboBox.Items.Count > 0)
            {
                printerComboBox.SelectedIndex = 0;
                _previousPrinter = printerComboBox.SelectedItem.ToString();
                PopulatePaperSizes();
            }
        }

        private void PopulatePaperSizes()
        {
            if (printerComboBox.SelectedItem == null) return;

            paperSizeComboBox.Items.Clear();
            string selectedPrinter = printerComboBox.SelectedItem.ToString();

            foreach (PaperSize paperSize in _printAppService.GetPaperSizes(selectedPrinter))
            {
                paperSizeComboBox.Items.Add(paperSize.PaperName);
            }

            if (paperSizeComboBox.Items.Count > 0)
            {
                paperSizeComboBox.SelectedIndex = 0;
            }
        }

        private void printerComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectedPrinter = printerComboBox.SelectedItem?.ToString();

            if (selectedPrinter != _previousPrinter)
            {
                _previousPrinter = selectedPrinter;
                PopulatePaperSizes();
                ApplyPrintSettings();
            }
        }

        private void paperSizeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyPrintSettings();
        }

        public void ApplyPrintSettings()
        {
            if (printerComboBox.SelectedItem != null)
            {
                _printAppService.SetPrinter(printerComboBox.SelectedItem.ToString());
            }
            else
            {
                _printAppService.ShowNotification(PrintResources.PrintError, PrintResources.NoPrinterSelected);
                return;
            }

            if (paperSizeComboBox.SelectedItem != null)
            {
                PaperSize selectedPaperSize = null;
                foreach (PaperSize paperSize in _printAppService.GetPaperSizes(printerComboBox.SelectedItem.ToString()))
                {
                    if (paperSize.PaperName == paperSizeComboBox.SelectedItem.ToString())
                    {
                        selectedPaperSize = paperSize;
                        break;
                    }
                }

                if (selectedPaperSize != null)
                {
                    _printAppService.SetPaperSize(selectedPaperSize);
                }
                else
                {
                    _printAppService.ShowNotification(PrintResources.PrintError,PrintResources.PaperSizeNotAvailable);
                }
            }
            else
            {
                _printAppService.ShowNotification(PrintResources.PrintError, PrintResources.NoPaperSizeSelected);
            }
        }







        [DllImport("printui.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int PrintUIEntry(IntPtr hwnd, IntPtr hinst, string lpszCmdLine, int nCmdShow);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int DocumentProperties(IntPtr hwnd, IntPtr hPrinter, string pDeviceName, IntPtr pDevModeOutput, IntPtr pDevModeInput, int fMode);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool OpenPrinter(string pPrinterName, out IntPtr phPrinter, IntPtr pDefault);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool ClosePrinter(IntPtr hPrinter);
        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool SetDefaultPrinter(string Name);
        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int DeviceCapabilities(string pDevice, string pPort, int fwCapability, IntPtr pOutput, IntPtr pDevMode);

        private const int DC_PAPERNAMES = 16;
        private const int DC_PAPERS = 2;



        private void button2_Click(object sender, EventArgs e)
        {
            ShowPrinterProperties(printerComboBox.SelectedItem.ToString());
        }

        private void ShowPrinterProperties(string printerName)
        {
            try
            {
                string arguments = $" /p /n \"{printerName}\"";
                int result = PrintUIEntry(IntPtr.Zero, IntPtr.Zero, arguments, 0);
                if (result != 0)
                {
                    _printAppService.ShowNotification(PrintResources.PrintError, PrintResources.PrinterPropertiesFailed);
                }
            }
            catch (Exception ex)
            {
                _printAppService.ShowNotification(PrintResources.PrintError, PrintResources.PrinterPropertiesError);
            }
        }

    }
}
