﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Mis.Agent.Localization {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class AgentResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AgentResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Mis.Agent.Localization.AgentResources", typeof(AgentResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent Notification.
        /// </summary>
        public static string AgentNotification {
            get {
                return ResourceManager.GetString("AgentNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success Process.
        /// </summary>
        public static string AgentSuccess {
            get {
                return ResourceManager.GetString("AgentSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent Url:.
        /// </summary>
        public static string AgentUrlLabel {
            get {
                return ResourceManager.GetString("AgentUrlLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Settings Saved.
        /// </summary>
        public static string AllSettingsSaved {
            get {
                return ResourceManager.GetString("AllSettingsSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Ports.
        /// </summary>
        public static string AvailablePorts {
            get {
                return ResourceManager.GetString("AvailablePorts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Ports:.
        /// </summary>
        public static string AvailablePortsLabel {
            get {
                return ResourceManager.GetString("AvailablePortsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Scanners.
        /// </summary>
        public static string AvailableScanners {
            get {
                return ResourceManager.GetString("AvailableScanners", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Settings Error.
        /// </summary>
        public static string BarcodeError {
            get {
                return ResourceManager.GetString("BarcodeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Form.
        /// </summary>
        public static string BarcodeForm {
            get {
                return ResourceManager.GetString("BarcodeForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Configuration.
        /// </summary>
        public static string BarcodeFormTitle {
            get {
                return ResourceManager.GetString("BarcodeFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Scanned.
        /// </summary>
        public static string BarcodeScanned {
            get {
                return ResourceManager.GetString("BarcodeScanned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode has been scanned successfully.
        /// </summary>
        public static string BarcodeScannedMessage {
            get {
                return ResourceManager.GetString("BarcodeScannedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Success.
        /// </summary>
        public static string BarcodeSuccess {
            get {
                return ResourceManager.GetString("BarcodeSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Tab.
        /// </summary>
        public static string BarcodeTab {
            get {
                return ResourceManager.GetString("BarcodeTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Tab.
        /// </summary>
        public static string BarcodeTab_Text {
            get {
                return ResourceManager.GetString("BarcodeTab.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Settings.
        /// </summary>
        public static string BarcodeTabText {
            get {
                return ResourceManager.GetString("BarcodeTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode URL.
        /// </summary>
        public static string BarcodeUrl {
            get {
                return ResourceManager.GetString("BarcodeUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Barcode URL.
        /// </summary>
        public static string BarcodeUrlInvalid {
            get {
                return ResourceManager.GetString("BarcodeUrlInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode URL:.
        /// </summary>
        public static string BarcodeUrlLabel {
            get {
                return ResourceManager.GetString("BarcodeUrlLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid Barcode URL.
        /// </summary>
        public static string BarcodeUrlMissing {
            get {
                return ResourceManager.GetString("BarcodeUrlMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save All Settings.
        /// </summary>
        public static string BtnSaveAllSettings_Text {
            get {
                return ResourceManager.GetString("BtnSaveAllSettings.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch Language.
        /// </summary>
        public static string BtnSwitchLanguage_Text {
            get {
                return ResourceManager.GetString("BtnSwitchLanguage.Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to clear all logs?.
        /// </summary>
        public static string ClearLogsConfirm {
            get {
                return ResourceManager.GetString("ClearLogsConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear Notifications.
        /// </summary>
        public static string ClearNotifications {
            get {
                return ResourceManager.GetString("ClearNotifications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear All Notifications.
        /// </summary>
        public static string ClearNotificationsButton {
            get {
                return ResourceManager.GetString("ClearNotificationsButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to clear all notifications?.
        /// </summary>
        public static string ClearNotificationsConfirm {
            get {
                return ResourceManager.GetString("ClearNotificationsConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COM Port.
        /// </summary>
        public static string ComPort {
            get {
                return ResourceManager.GetString("ComPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COM Port:.
        /// </summary>
        public static string ComPortLabel {
            get {
                return ResourceManager.GetString("ComPortLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a COM Port.
        /// </summary>
        public static string ComPortMissing {
            get {
                return ResourceManager.GetString("ComPortMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save configuration.
        /// </summary>
        public static string ConfigurationFailed {
            get {
                return ResourceManager.GetString("ConfigurationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration saved successfully.
        /// </summary>
        public static string ConfigurationSaved {
            get {
                return ResourceManager.GetString("ConfigurationSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmation.
        /// </summary>
        public static string Confirmation {
            get {
                return ResourceManager.GetString("Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection Failed.
        /// </summary>
        public static string ConnectionFailed {
            get {
                return ResourceManager.GetString("ConnectionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to establish connection.
        /// </summary>
        public static string ConnectionFailedMessage {
            get {
                return ResourceManager.GetString("ConnectionFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection Successful.
        /// </summary>
        public static string ConnectionSuccess {
            get {
                return ResourceManager.GetString("ConnectionSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection established successfully.
        /// </summary>
        public static string ConnectionSuccessMessage {
            get {
                return ResourceManager.GetString("ConnectionSuccessMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Paper Size.
        /// </summary>
        public static string CurrentPaperSize {
            get {
                return ResourceManager.GetString("CurrentPaperSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Printer.
        /// </summary>
        public static string CurrentPrinter {
            get {
                return ResourceManager.GetString("CurrentPrinter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database error.
        /// </summary>
        public static string DatabaseError {
            get {
                return ResourceManager.GetString("DatabaseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Printer.
        /// </summary>
        public static string DefaultPrinter {
            get {
                return ResourceManager.GetString("DefaultPrinter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Printer:.
        /// </summary>
        public static string DefaultPrinterLabel {
            get {
                return ResourceManager.GetString("DefaultPrinterLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable/Disable Notifications.
        /// </summary>
        public static string EnableNotifications {
            get {
                return ResourceManager.GetString("EnableNotifications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable/Disable Notifications.
        /// </summary>
        public static string EnableNotificationsCheckbox {
            get {
                return ResourceManager.GetString("EnableNotificationsCheckbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error In Capture Image In Agent Starter.
        /// </summary>
        public static string ErrorInCaptureImageInAgentStarter {
            get {
                return ResourceManager.GetString("ErrorInCaptureImageInAgentStarter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Loading Plugin Tabs.
        /// </summary>
        public static string ErrorLoadingPluginTabs {
            get {
                return ResourceManager.GetString("ErrorLoadingPluginTabs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred.
        /// </summary>
        public static string ErrorMessage {
            get {
                return ResourceManager.GetString("ErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred.
        /// </summary>
        public static string ErrorOccurred {
            get {
                return ResourceManager.GetString("ErrorOccurred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Refreshing Notifications Data Message.
        /// </summary>
        public static string ErrorRefreshingNotificationsDataMessage {
            get {
                return ResourceManager.GetString("ErrorRefreshingNotificationsDataMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving Settings.
        /// </summary>
        public static string ErrorSavingSettings {
            get {
                return ResourceManager.GetString("ErrorSavingSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MIS Agent.
        /// </summary>
        public static string FormTitle {
            get {
                return ResourceManager.GetString("FormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to connect to Hub: No connection could be made because the target machine actively refused it..
        /// </summary>
        public static string HubConnectionFailed {
            get {
                return ResourceManager.GetString("HubConnectionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub Connection Success.
        /// </summary>
        public static string HubConnectionSuccess {
            get {
                return ResourceManager.GetString("HubConnectionSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information.
        /// </summary>
        public static string Information {
            get {
                return ResourceManager.GetString("Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click the button above to switch between English and Arabic. Notice how the text changes and the layout adjusts for RTL..
        /// </summary>
        public static string InstructionsMessage {
            get {
                return ResourceManager.GetString("InstructionsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid configuration.
        /// </summary>
        public static string InvalidConfiguration {
            get {
                return ResourceManager.GetString("InvalidConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid port number between 1 and 65535..
        /// </summary>
        public static string InvalidPortFormat {
            get {
                return ResourceManager.GetString("InvalidPortFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid URL.
        /// </summary>
        public static string InvalidUrl {
            get {
                return ResourceManager.GetString("InvalidUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid Barcode URL..
        /// </summary>
        public static string InvalidUrlMessage {
            get {
                return ResourceManager.GetString("InvalidUrlMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Printed.
        /// </summary>
        public static string IsPrinted {
            get {
                return ResourceManager.GetString("IsPrinted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language Switched.
        /// </summary>
        public static string LanguageSwitched {
            get {
                return ResourceManager.GetString("LanguageSwitched", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language has been switched successfully..
        /// </summary>
        public static string LanguageSwitchedMessage {
            get {
                return ResourceManager.GetString("LanguageSwitchedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Localization Test.
        /// </summary>
        public static string LocalizationTestTitle {
            get {
                return ResourceManager.GetString("LocalizationTestTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All logs cleared successfully.
        /// </summary>
        public static string LogsCleared {
            get {
                return ResourceManager.GetString("LogsCleared", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent Form .
        /// </summary>
        public static string MainFormTitle {
            get {
                return ResourceManager.GetString("MainFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No data to print.
        /// </summary>
        public static string NoDataToPrint {
            get {
                return ResourceManager.GetString("NoDataToPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Plugins Are Loaded Please Restart The Application.
        /// </summary>
        public static string NoPluginsAreLoadedPleaseRestartTheApplication {
            get {
                return ResourceManager.GetString("NoPluginsAreLoadedPleaseRestartTheApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a COM port..
        /// </summary>
        public static string NoPortSelectedMessage {
            get {
                return ResourceManager.GetString("NoPortSelectedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        public static string NotificationId {
            get {
                return ResourceManager.GetString("NotificationId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number.
        /// </summary>
        public static string NotificationNumber {
            get {
                return ResourceManager.GetString("NotificationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All notifications cleared successfully.
        /// </summary>
        public static string NotificationsCleared {
            get {
                return ResourceManager.GetString("NotificationsCleared", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications Management.
        /// </summary>
        public static string NotificationsFormTitle {
            get {
                return ResourceManager.GetString("NotificationsFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications sent successfully.
        /// </summary>
        public static string NotificationsSent {
            get {
                return ResourceManager.GetString("NotificationsSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications Settings.
        /// </summary>
        public static string NotificationsTab {
            get {
                return ResourceManager.GetString("NotificationsTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications Settings.
        /// </summary>
        public static string NotificationsTabText {
            get {
                return ResourceManager.GetString("NotificationsTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string OK {
            get {
                return ResourceManager.GetString("OK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paper Size.
        /// </summary>
        public static string PaperSize {
            get {
                return ResourceManager.GetString("PaperSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paper Type:.
        /// </summary>
        public static string PaperType {
            get {
                return ResourceManager.GetString("PaperType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Picture Box Not Found.
        /// </summary>
        public static string PictureBoxNotFound {
            get {
                return ResourceManager.GetString("PictureBoxNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port Connected.
        /// </summary>
        public static string PortConnected {
            get {
                return ResourceManager.GetString("PortConnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port connection established successfully.
        /// </summary>
        public static string PortConnectedMessage {
            get {
                return ResourceManager.GetString("PortConnectedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port Disconnected.
        /// </summary>
        public static string PortDisconnected {
            get {
                return ResourceManager.GetString("PortDisconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port has been disconnected.
        /// </summary>
        public static string PortDisconnectedMessage {
            get {
                return ResourceManager.GetString("PortDisconnectedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port Error.
        /// </summary>
        public static string PortError {
            get {
                return ResourceManager.GetString("PortError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port In Use.
        /// </summary>
        public static string PortInUse {
            get {
                return ResourceManager.GetString("PortInUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port.
        /// </summary>
        public static string PortTab {
            get {
                return ResourceManager.GetString("PortTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port Settings.
        /// </summary>
        public static string PortTabText {
            get {
                return ResourceManager.GetString("PortTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print completed successfully.
        /// </summary>
        public static string PrintCompleted {
            get {
                return ResourceManager.GetString("PrintCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer Name.
        /// </summary>
        public static string PrinterName {
            get {
                return ResourceManager.GetString("PrinterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer Name:.
        /// </summary>
        public static string PrinterNameLabel {
            get {
                return ResourceManager.GetString("PrinterNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer not found.
        /// </summary>
        public static string PrinterNotFound {
            get {
                return ResourceManager.GetString("PrinterNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print failed.
        /// </summary>
        public static string PrintFailed {
            get {
                return ResourceManager.GetString("PrintFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Configuration.
        /// </summary>
        public static string PrintFormTitle {
            get {
                return ResourceManager.GetString("PrintFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Job Completed.
        /// </summary>
        public static string PrintJobCompleted {
            get {
                return ResourceManager.GetString("PrintJobCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print job has been completed successfully.
        /// </summary>
        public static string PrintJobCompletedMessage {
            get {
                return ResourceManager.GetString("PrintJobCompletedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        public static string PrintTab {
            get {
                return ResourceManager.GetString("PrintTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Settings.
        /// </summary>
        public static string PrintTabText {
            get {
                return ResourceManager.GetString("PrintTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Properties.
        /// </summary>
        public static string Properties {
            get {
                return ResourceManager.GetString("Properties", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Time.
        /// </summary>
        public static string ReceiveTime {
            get {
                return ResourceManager.GetString("ReceiveTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restart Required.
        /// </summary>
        public static string RestartRequired {
            get {
                return ResourceManager.GetString("RestartRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Barcode Configuration.
        /// </summary>
        public static string SaveBarcodeConfigButton {
            get {
                return ResourceManager.GetString("SaveBarcodeConfigButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Barcode Configuration.
        /// </summary>
        public static string SaveBarcodeConfiguration {
            get {
                return ResourceManager.GetString("SaveBarcodeConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Port Configuration.
        /// </summary>
        public static string SavePortConfigButton {
            get {
                return ResourceManager.GetString("SavePortConfigButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Print Configuration.
        /// </summary>
        public static string SavePrintConfigButton {
            get {
                return ResourceManager.GetString("SavePrintConfigButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Print Configuration.
        /// </summary>
        public static string SavePrintConfiguration {
            get {
                return ResourceManager.GetString("SavePrintConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Scanner Configuration.
        /// </summary>
        public static string SaveScannerConfiguration {
            get {
                return ResourceManager.GetString("SaveScannerConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanned Image.
        /// </summary>
        public static string ScannedImage {
            get {
                return ResourceManager.GetString("ScannedImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner connected successfully.
        /// </summary>
        public static string ScannerConnected {
            get {
                return ResourceManager.GetString("ScannerConnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner disconnected.
        /// </summary>
        public static string ScannerDisconnected {
            get {
                return ResourceManager.GetString("ScannerDisconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner not found.
        /// </summary>
        public static string ScannerNotFound {
            get {
                return ResourceManager.GetString("ScannerNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner.
        /// </summary>
        public static string ScannerTab {
            get {
                return ResourceManager.GetString("ScannerTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Manual Data.
        /// </summary>
        public static string SendManualData {
            get {
                return ResourceManager.GetString("SendManualData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings Saved.
        /// </summary>
        public static string SettingsSaved {
            get {
                return ResourceManager.GetString("SettingsSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings have been saved successfully.
        /// </summary>
        public static string SettingsSavedMessage {
            get {
                return ResourceManager.GetString("SettingsSavedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings updated successfully.
        /// </summary>
        public static string SettingsUpdated {
            get {
                return ResourceManager.GetString("SettingsUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings are already up to date..
        /// </summary>
        public static string SettingsUpToDate {
            get {
                return ResourceManager.GetString("SettingsUpToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success.
        /// </summary>
        public static string Success {
            get {
                return ResourceManager.GetString("Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch Language Error.
        /// </summary>
        public static string SwitchLanguageError {
            get {
                return ResourceManager.GetString("SwitchLanguageError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch to Arabic.
        /// </summary>
        public static string SwitchToArabic {
            get {
                return ResourceManager.GetString("SwitchToArabic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch to English.
        /// </summary>
        public static string SwitchToEnglish {
            get {
                return ResourceManager.GetString("SwitchToEnglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner.
        /// </summary>
        public static string TabScanner {
            get {
                return ResourceManager.GetString("TabScanner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TabsForm Is Already Opened.
        /// </summary>
        public static string TabsFormIsAlreadyOpened {
            get {
                return ResourceManager.GetString("TabsFormIsAlreadyOpened", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TabsForm Operation Failed.
        /// </summary>
        public static string TabsFormOperationFailed {
            get {
                return ResourceManager.GetString("TabsFormOperationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Connection.
        /// </summary>
        public static string TestConnection {
            get {
                return ResourceManager.GetString("TestConnection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Connection.
        /// </summary>
        public static string TestConnectionButton {
            get {
                return ResourceManager.GetString("TestConnectionButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Print.
        /// </summary>
        public static string TestPrint {
            get {
                return ResourceManager.GetString("TestPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Error.
        /// </summary>
        public static string UpdateError {
            get {
                return ResourceManager.GetString("UpdateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Notification.
        /// </summary>
        public static string UpdateNotification {
            get {
                return ResourceManager.GetString("UpdateNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use Barcode Reader.
        /// </summary>
        public static string UseBarcodeReader {
            get {
                return ResourceManager.GetString("UseBarcodeReader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation Error Message.
        /// </summary>
        public static string ValidationErrorMessage {
            get {
                return ResourceManager.GetString("ValidationErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string Warning {
            get {
                return ResourceManager.GetString("Warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Localization Test.
        /// </summary>
        public static string WelcomeMessage {
            get {
                return ResourceManager.GetString("WelcomeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
