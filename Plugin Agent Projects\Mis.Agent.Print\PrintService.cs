using IronPdf;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Mis.Agent.Print;
using Mis.Agent.Print.Resources;
using Mis.Shared.Interface;
using Newtonsoft.Json;
using PdfiumViewer;
using PuppeteerSharp;
using PuppeteerSharp.Media;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Drawing.Printing;
using System.Linq;
using System.Runtime;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Mis.Agent.Print
{
    [Plugin("Print & Notifications", Version = "1.0.0", Description = "Printing and notifications management", Order = 30)]

    public class PrintService : IPrintAppService
    {
        public PrintDocument PrintDocument { get; private set; }
        PrintForm printForm;
        private readonly IConfiguration _configuration;
        PrintJobMonitor printJobMonitor;
        HtmlToPdf? renderer;
        private bool _notificationsEnabled;
        private TaskCompletionSource<bool> _taskCompletionSource;
        private readonly string _appsettingsFilePath;
        public event Action<bool> NotificationStateChanged;
        private ILogger<PrintService> _logger;

        public PrintService(ILogger<PrintService> logger)
        {
            _logger = logger;
            var baseDirectory = AppContext.BaseDirectory;
            var appsettingsFilePath = Path.Combine(baseDirectory, "appsettings.json");
            if (!File.Exists(appsettingsFilePath))
            {
                MessageBox.Show($"Missing configuration: {appsettingsFilePath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }
            _appsettingsFilePath = appsettingsFilePath;

            _configuration = new ConfigurationBuilder()
                .SetBasePath(baseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            renderer = new HtmlToPdf();
            printJobMonitor = new PrintJobMonitor();
            PrintDocument = new PrintDocument();
            _taskCompletionSource = new TaskCompletionSource<bool>();
            NotificationManager.NotificationEvent += OnNotificationReceived;
            GetNotificationEnabledSetting();
            printForm = new PrintForm(this);

        }
        public void GetNotificationEnabledSetting()
        {
            var section = _configuration.GetSection("NotificationSettings");
            _notificationsEnabled = section.GetValue<bool?>("EnabeledNotification") ?? false;
        }
        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }


        [PluginNotificationHandler]
        public void ShowNotification(string title, string text)
        {
            // Use centralized NotificationManager for consistent localization
            NotificationManager.SetNotificationEnabled(_notificationsEnabled);
            NotificationManager.ShowNotification(title, text);
        }

        public PrinterEntity GetPrinterEntity()
        {
            return _configuration.GetSection("PrinterEntity").Get<PrinterEntity>();
        }




        [PluginTabProvider]
        public object GetTabPage()
        {
            var freshForm = new PrintForm(this);
            freshForm.PopulateForm(); // Populate form elements

            return freshForm.PrintTab; // Return the TabPage as an object
        }
        [PluginInitialize]
        public async Task Initialize()
        {
            printForm.PopulateForm();
        }
        public async Task<bool> PrintAsync(TransactionDto transactionDto)
        {
            try
            {
                // Get the printer settings from SettingsManager
                PrinterEntity printerSettings = GetPrinterEntity();
                string defaultPrinter = printerSettings.DefaultPrinter;
                string defaultPaperSize = printerSettings.DefaultPaperSize;

                // Set up the printer and paper size
                var paperSize = ConfigurePrinterSettings(defaultPrinter, defaultPaperSize);

                if (!string.IsNullOrEmpty(transactionDto?.HtmlContent))
                {
                    // Generate the PDF file from HTML content
                    string tempFilePath = await GeneratePdfFile(transactionDto.HtmlContent, paperSize);

                    // Print the PDF file and delete after printing
                    bool isPrinted = PrintPdfAndMonitorJobAsync(tempFilePath, defaultPrinter, paperSize);
                    ShowNotification(PrintResources.Success, PrintResources.PrintSuccess);

                    return isPrinted;
                }
                else
                {
                    ShowNotification(PrintResources.Error, PrintResources.PrintFail);

                    return false;
                }
            }
            catch (Exception ex)
            {
                ShowNotification(PrintResources.Error, PrintResources.PrintingError);
                _logger.LogError($"An error occurred during printing: {ex.Message}");

                return false;
            }
        }







        public void SetPrinter(string printerName)
        {
            PrintDocument.PrinterSettings.PrinterName = printerName;
        }

        public void SetPaperSize(PaperSize paperSize)
        {
            PrintDocument.DefaultPageSettings.PaperSize = paperSize;
        }
        public string GetDefaultPrinter()
        {
            // Create a PrinterSettings object which automatically picks up the default printer
            PrinterSettings settings = new PrinterSettings();

            // Return the name of the default printer
            return settings.PrinterName;
        }

        public IEnumerable<string> GetInstalledPrinters()
        {
            return PrinterSettings.InstalledPrinters.Cast<string>();
        }

        public PaperSize[] GetPaperSizes(string printerName)
        {
            using (var tempPrintDocument = new PrintDocument())
            {
                tempPrintDocument.PrinterSettings.PrinterName = printerName;
                return tempPrintDocument.PrinterSettings.PaperSizes.Cast<PaperSize>().ToArray();
            }
        }






        public bool PrintPdf(string pdfFilePath, string printerName, PaperSize paperSize)
        {
            try
            {
                using (var pdfDocument = PdfiumViewer.PdfDocument.Load(pdfFilePath))
                {
                    // Create a PrintDocument from PdfiumViewer
                    var printDocument = pdfDocument.CreatePrintDocument();
                    // Set page settings
                    printDocument.DefaultPageSettings = new PageSettings
                    {
                        PaperSize = paperSize,
                        //Landscape = true // Set to true for landscape orientation
                    };
                    printDocument.PrinterSettings.PrinterName = printerName;


                    // Print the document
                    printDocument.Print();

                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error printing PDF: {ex.Message}");
                return false;
            }
        }







        private PaperSize ConfigurePrinterSettings(string printerName, string paperSizeName)
        {
            var paperSizes = GetPaperSizes(printerName).ToList();

            if (!string.IsNullOrEmpty(paperSizeName))
            {
                var selectedPaperSize = paperSizes.FirstOrDefault(p => p.PaperName.Equals(paperSizeName, StringComparison.OrdinalIgnoreCase));

                if (selectedPaperSize != null)
                {
                    SetPaperSize(selectedPaperSize);
                    return selectedPaperSize;
                }
                else
                {
                    Console.WriteLine("Default paper size not found. Using the first available paper size.");
                }
            }
            else
            {
                Console.WriteLine("Default paper size is not set in settings.");
            }

            // Fallback to the first available paper size if the specified one is not found
            var fallbackPaperSize = paperSizes.FirstOrDefault();
            if (fallbackPaperSize != null)
            {
                SetPaperSize(fallbackPaperSize);
            }

            return fallbackPaperSize;
        }

        private async Task<string> GeneratePdfFile(string htmlContent, PaperSize paperSize)
        {
            try
            {
                // Get localized strings from .resx
                string title = PrintResources.PrintingError;
                string message = PrintResources.HTMLContent;

                if (string.IsNullOrWhiteSpace(htmlContent))
                {
                    NotificationManager.ShowNotification("PrintingError", "HTMLContent", title, message);
                    throw new ArgumentException(message);
                }

                // Log HTML content for debugging
                _logger.LogInformation($"HTML Content Length: {htmlContent.Length}");
                _logger.LogInformation($"HTML Content Preview: {htmlContent.Substring(0, Math.Min(200, htmlContent.Length))}...");

                string tempFilePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".pdf");

                // Configure IronPDF before use
                try
                {
                    // Set a safe temp folder path for IronPDF
                    var tempPath = Path.Combine(Path.GetTempPath(), "IronPDF");
                    if (!Directory.Exists(tempPath))
                    {
                        Directory.CreateDirectory(tempPath);
                    }
                    IronPdf.Installation.TempFolderPath = tempPath;
                    _logger.LogInformation($"IronPDF temp folder set to: {tempPath}");

                    // Try to set the binary path for IronPDF dependencies
                    var baseDirectory = AppContext.BaseDirectory;
                    var ironPdfBinPath = Path.Combine(baseDirectory, "bin", "IronPdf");
                    if (Directory.Exists(ironPdfBinPath))
                    {
                        _logger.LogInformation($"IronPDF binary path found: {ironPdfBinPath}");
                        // Add the IronPDF bin directory to PATH
                        var currentPath = Environment.GetEnvironmentVariable("PATH");
                        Environment.SetEnvironmentVariable("PATH", $"{ironPdfBinPath};{currentPath}");
                    }

                    // Set license if available
                    // IronPdf.License.LicenseKey = "IRONPDF-BOARD4ALL.BIZ-120981-6E5672-8683679C43-3138BC52-NEx-T24";
                    _logger.LogInformation("Running IronPDF in trial mode");
                }
                catch (Exception configEx)
                {
                    _logger.LogError($"Failed to configure IronPDF: {configEx.Message}");
                    // If IronPDF configuration fails, skip to fallback method
                    _logger.LogInformation("Skipping IronPDF due to configuration issues, using fallback method");
                    return await GeneratePdfFallback(htmlContent, tempFilePath);
                }

                var printOptions = new PdfPrintOptions
                {
                    InputEncoding = Encoding.UTF8,
                    FitToPaperWidth = true,
                    Zoom = 400,
                    MarginLeft = 0,
                    MarginRight = 0,
                    MarginTop = 0,
                    MarginBottom = 10,
                };

                // Configure paper size if provided
                if (paperSize != null)
                {
                    // Convert from hundredths of an inch to millimeters
                    double widthInMm = (paperSize.Width / 100.0) * 25.4;
                    double heightInMm = (paperSize.Height / 100.0) * 25.4;

                    _logger.LogInformation($"Paper Size: {paperSize.PaperName}, Width: {widthInMm}mm, Height: {heightInMm}mm");
                    printOptions.SetCustomPaperSizeinMilimeters(widthInMm, heightInMm);
                }

                var renderer = new HtmlToPdf(printOptions);

                // Add validation for HTML content
                if (!IsValidHtml(htmlContent))
                {
                    _logger.LogWarning("HTML content appears to be invalid, wrapping in basic HTML structure");
                    htmlContent = $"<html><head><meta charset='utf-8'></head><body>{htmlContent}</body></html>";
                }

                _logger.LogInformation("Starting PDF generation...");

                // Try to generate PDF with IronPDF
                PdfDocument pdfDocument = null;
                try
                {
                    pdfDocument = renderer.RenderHtmlAsPdf(htmlContent);
                }
                catch (Exception ironPdfEx)
                {
                    _logger.LogError($"IronPDF rendering failed: {ironPdfEx.Message}");
                    _logger.LogInformation("Falling back to PuppeteerSharp for PDF generation");
                    return await GeneratePdfFallback(htmlContent, tempFilePath);
                }

                // Check if PDF was generated successfully
                if (pdfDocument == null)
                {
                    _logger.LogError("PDF document is null - trying fallback method");
                    return await GeneratePdfFallback(htmlContent, tempFilePath);
                }

                if (pdfDocument.BinaryData == null || pdfDocument.BinaryData.Length == 0)
                {
                    _logger.LogError("PDF binary data is empty - trying fallback method");
                    return await GeneratePdfFallback(htmlContent, tempFilePath);
                }

                _logger.LogInformation($"PDF generated successfully. Size: {pdfDocument.BinaryData.Length} bytes");
                File.WriteAllBytes(tempFilePath, pdfDocument.BinaryData);

                return tempFilePath;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error generating PDF: {ex.Message}");
                _logger.LogError($"Stack trace: {ex.StackTrace}");
                throw new Exception($"PDF generation failed: {ex.Message}", ex);
            }
        }

        private bool IsValidHtml(string htmlContent)
        {
            // Basic HTML validation
            return htmlContent.Trim().StartsWith("<") && htmlContent.Trim().EndsWith(">");
        }

        private async Task<string> GeneratePdfFallback(string htmlContent, string tempFilePath)
        {
            try
            {
                _logger.LogInformation("Using PuppeteerSharp fallback PDF generation method...");

                // Ensure HTML has proper structure
                if (!IsValidHtml(htmlContent))
                {
                    htmlContent = $"<html><head><meta charset='utf-8'><style>body{{font-family: Arial, sans-serif; margin: 10px;}}</style></head><body>{htmlContent}</body></html>";
                }

                // Use PuppeteerSharp for PDF generation
                await new BrowserFetcher().DownloadAsync();

                using var browser = await Puppeteer.LaunchAsync(new LaunchOptions
                {
                    Headless = true,
                    Args = new[] { "--no-sandbox", "--disable-setuid-sandbox" }
                });

                using var page = await browser.NewPageAsync();
                await page.SetContentAsync(htmlContent);

                var pdfOptions = new PdfOptions
                {
                    Format = PaperFormat.A4,
                    PrintBackground = true,
                    MarginOptions = new MarginOptions
                    {
                        Top = "10mm",
                        Bottom = "10mm",
                        Left = "10mm",
                        Right = "10mm"
                    }
                };

                var pdfBytes = await page.PdfDataAsync(pdfOptions);
                await File.WriteAllBytesAsync(tempFilePath, pdfBytes);

                _logger.LogInformation($"PuppeteerSharp PDF generated successfully. Size: {pdfBytes.Length} bytes");
                return tempFilePath;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PuppeteerSharp PDF generation failed: {ex.Message}");

                // Last resort - create a simple text file
                CreateSimplePdf(htmlContent, tempFilePath);
                return tempFilePath;
            }
        }

        private void CreateSimplePdf(string content, string filePath)
        {
            // This is a very basic fallback - you might want to use a different PDF library
            // For now, we'll create a minimal PDF structure
            var pdfContent = $@"%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length {content.Length + 50}
>>
stream
BT
/F1 12 Tf
50 750 Td
({content.Replace("\n", " ").Replace("\r", " ")}) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000010 00000 n
0000000053 00000 n
0000000125 00000 n
0000000185 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
{300 + content.Length}
%%EOF";

            File.WriteAllText(filePath, pdfContent);
            _logger.LogInformation("Created simple fallback PDF");
        }

        private bool PrintPdfAndMonitorJobAsync(string filePath, string printerName, PaperSize paperSize)
        {
            bool printSuccess = false;

            try
            {
                printSuccess = PrintPdf(filePath, printerName, paperSize);

                if (printSuccess)
                {
                    return printJobMonitor.MonitorPrintJobAsync(printerName);
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
                throw; // Re-throw the exception after setting the task result
            }
            finally
            {
                // Ensure the file is deleted whether the print succeeds or fails
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }









    }
}
