{"format": 1, "restore": {"C:\\Mis Agent\\Mis.Agent\\Mis.Agent.csproj": {}}, "projects": {"C:\\Mis Agent\\Mis.Agent\\Mis.Agent.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Mis Agent\\Mis.Agent\\Mis.Agent.csproj", "projectName": "Mis.Agent", "projectPath": "C:\\Mis Agent\\Mis.Agent\\Mis.Agent.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Mis Agent\\Mis.Agent\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"C:\\Mis Agent\\Plugin Agent Projects\\Mis.Shared.Interface\\Mis.Shared.Interface.csproj": {"projectPath": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Shared.Interface\\Mis.Shared.Interface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.Owin": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.SignalR.Client.Core": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[9.0.6, )"}, "PdfSharpCore": {"target": "Package", "version": "[1.3.67, )"}, "PdfiumViewer.Core": {"target": "Package", "version": "[1.0.4, )"}, "PuppeteerSharp": {"target": "Package", "version": "[20.2.2, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[2.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.8.0, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.119, )"}, "System.Security.Cryptography.Pkcs": {"target": "Package", "version": "[9.0.6, )"}, "System.Security.Cryptography.X509Certificates": {"target": "Package", "version": "[4.3.2, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Shared.Interface\\Mis.Shared.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Shared.Interface\\Mis.Shared.Interface.csproj", "projectName": "Mis.Shared.Interface", "projectPath": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Shared.Interface\\Mis.Shared.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Mis Agent\\Plugin Agent Projects\\Mis.Shared.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.119, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.6, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}