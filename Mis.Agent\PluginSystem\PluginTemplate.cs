using System;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;

namespace Mis.Agent.PluginSystem
{
    /// <summary>
    /// Template for creating new plugins using the reflection-based plugin system
    /// Copy this template and modify it to create your own plugin
    /// </summary>
    [Plugin("Template Plugin", Version = "1.0.0", Description = "A template plugin showing how to create plugins", Order = 999)]
    public class PluginTemplate
    {
        private readonly IServiceProvider? _serviceProvider;
        private UserControl? _mainControl;

        /// <summary>
        /// Constructor - can accept IServiceProvider for dependency injection
        /// </summary>
        public PluginTemplate(IServiceProvider? serviceProvider = null)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// This method is called when the plugin is loaded
        /// Use this for initialization logic
        /// </summary>
        [PluginInitialize]
        public void Initialize()
        {
            // Initialize your plugin here
            Console.WriteLine("Template Plugin initialized");
            
            // You can access services from the service provider if needed
            // var someService = _serviceProvider?.GetService<ISomeService>();
        }

        /// <summary>
        /// This method should return the TabPage or UserControl for your plugin
        /// This will be displayed as a tab in the main application
        /// </summary>
        [PluginTabProvider]
        public UserControl GetTabPage()
        {
            if (_mainControl == null)
            {
                _mainControl = CreateMainControl();
            }
            return _mainControl;
        }

        /// <summary>
        /// This method is called when the plugin is unloaded
        /// Use this for cleanup logic
        /// </summary>
        [PluginCleanup]
        public void Cleanup()
        {
            // Clean up resources here
            Console.WriteLine("Template Plugin cleaned up");
            _mainControl?.Dispose();
            _mainControl = null;
        }

        /// <summary>
        /// This method handles notifications
        /// The plugin system will call this when showing notifications
        /// </summary>
        [PluginNotificationHandler]
        public void ShowNotification(string title, string text)
        {
            // Handle notifications here
            // You can show a balloon tip, update UI, log, etc.
            Console.WriteLine($"Notification: {title} - {text}");
            
            // Example: Show a balloon tip
            using (var notifyIcon = new NotifyIcon
            {
                Icon = SystemIcons.Information,
                Visible = true,
                BalloonTipTitle = title,
                BalloonTipText = text
            })
            {
                notifyIcon.ShowBalloonTip(3000);
                Task.Delay(3000).ContinueWith(t => notifyIcon.Dispose());
            }
        }

        /// <summary>
        /// Configuration method example - returns the base URL
        /// The key "BaseUrl" can be used to retrieve this value
        /// </summary>
        [PluginConfiguration("BaseUrl")]
        public string GetBaseUrl()
        {
            // Return your plugin's base URL configuration
            return "https://localhost:5000";
        }

        /// <summary>
        /// Configuration method example - returns the COM port
        /// The key "ComPort" can be used to retrieve this value
        /// </summary>
        [PluginConfiguration("ComPort")]
        public string GetCOMPort()
        {
            // Return your plugin's COM port configuration
            return "COM1";
        }

        /// <summary>
        /// Configuration method example - returns the barcode base URL
        /// The key "BarcodeBaseUrl" can be used to retrieve this value
        /// </summary>
        [PluginConfiguration("BarcodeBaseUrl")]
        public string GetBarcodeBaseUrl()
        {
            // Return your plugin's barcode base URL configuration
            return "https://localhost:5001/barcode";
        }

        /// <summary>
        /// Creates the main control for the plugin
        /// Customize this method to create your plugin's UI
        /// </summary>
        private UserControl CreateMainControl()
        {
            var control = new UserControl
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };

            // Add your controls here
            var label = new Label
            {
                Text = "Template Plugin",
                Font = new Font("Arial", 16, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var button = new Button
            {
                Text = "Click Me",
                Location = new Point(20, 60),
                Size = new Size(100, 30)
            };

            button.Click += (s, e) => MessageBox.Show("Hello from Template Plugin!");

            control.Controls.Add(label);
            control.Controls.Add(button);

            return control;
        }
    }
}
