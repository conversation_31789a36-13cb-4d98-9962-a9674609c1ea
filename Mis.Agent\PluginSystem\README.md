# Professional Plugin System Documentation

## Overview

This plugin system uses pure reflection without shared interface dependencies. Plugins are discovered and loaded automatically from the `Plugins` folder using conventions and attributes.

## Key Features

- **No Shared Interfaces**: Plugins don't need to reference any shared interface libraries
- **Convention-Based**: Uses attributes and naming conventions for plugin discovery
- **Dependency Injection**: Supports constructor injection for plugins
- **Automatic Discovery**: Automatically discovers and loads plugins from DLL files
- **Tab Integration**: Plugins can provide tabs for the main application
- **Configuration Support**: Plugins can provide configuration values
- **Notification System**: Plugins can handle notifications
- **Lifecycle Management**: Supports initialization and cleanup

## Creating a Plugin

### 1. Create a New Class Library Project

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <OutputPath>..\..\Mis.Agent\bin\Debug\net9.0-windows\Plugins\</OutputPath>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
  </ItemGroup>
</Project>
```

### 2. Create Your Plugin Class

```csharp
using System;
using System.Windows.Forms;

namespace MyPlugin
{
    [Plugin("My Plugin", Version = "1.0.0", Description = "My awesome plugin", Order = 10)]
    public class MyPluginClass
    {
        [PluginTabProvider]
        public UserControl GetTabPage()
        {
            // Return your plugin's UI
            return new MyPluginControl();
        }

        [PluginInitialize]
        public void Initialize()
        {
            // Initialize your plugin
        }

        [PluginCleanup]
        public void Cleanup()
        {
            // Clean up resources
        }

        [PluginNotificationHandler]
        public void ShowNotification(string title, string text)
        {
            // Handle notifications
        }

        [PluginConfiguration("BaseUrl")]
        public string GetBaseUrl()
        {
            return "https://myapi.com";
        }
    }
}
```

## Attributes Reference

### `[Plugin]` - Class Attribute
Marks a class as a plugin. **Required** for plugin discovery.

**Properties:**
- `Name` (required): Display name for the plugin
- `Version`: Plugin version (default: "1.0.0")
- `Description`: Plugin description
- `Order`: Tab order (lower numbers appear first, default: 100)
- `AutoLoad`: Whether to load automatically (default: true)

### `[PluginTabProvider]` - Method Attribute
Marks a method that returns the plugin's UI component.

**Requirements:**
- Method must return `TabPage` or `UserControl`
- Method should take no parameters
- Only one method per plugin should have this attribute

### `[PluginInitialize]` - Method Attribute
Marks a method to be called when the plugin is loaded.

**Requirements:**
- Method should take no parameters
- Method should return `void`
- Optional - plugins don't need initialization

### `[PluginCleanup]` - Method Attribute
Marks a method to be called when the plugin is unloaded.

**Requirements:**
- Method should take no parameters
- Method should return `void`
- Optional - plugins don't need cleanup

### `[PluginNotificationHandler]` - Method Attribute
Marks a method that handles notifications.

**Requirements:**
- Method signature: `void MethodName(string title, string text)`
- Optional - plugins don't need to handle notifications

### `[PluginConfiguration]` - Method Attribute
Marks a method that provides configuration values.

**Properties:**
- `Key` (required): Configuration key name

**Requirements:**
- Method should take no parameters
- Method can return any type
- Multiple configuration methods allowed per plugin

## Plugin Lifecycle

1. **Discovery**: Plugin manager scans DLL files in the Plugins folder
2. **Metadata Creation**: Extracts plugin information using reflection
3. **Loading**: Creates plugin instances using dependency injection
4. **Initialization**: Calls methods marked with `[PluginInitialize]`
5. **Tab Creation**: Calls methods marked with `[PluginTabProvider]`
6. **Runtime**: Plugin is active and can handle notifications/configurations
7. **Cleanup**: Calls methods marked with `[PluginCleanup]` when unloading

## Best Practices

### 1. Error Handling
Always wrap plugin code in try-catch blocks:

```csharp
[PluginTabProvider]
public UserControl GetTabPage()
{
    try
    {
        return CreateMyControl();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error creating tab: {ex.Message}");
        return new Label { Text = "Error loading plugin" };
    }
}
```

### 2. Resource Management
Properly dispose of resources in cleanup:

```csharp
private Timer _timer;

[PluginInitialize]
public void Initialize()
{
    _timer = new Timer();
}

[PluginCleanup]
public void Cleanup()
{
    _timer?.Dispose();
    _timer = null;
}
```

### 3. Dependency Injection
Use constructor injection for services:

```csharp
public class MyPlugin
{
    private readonly IMyService _myService;

    public MyPlugin(IMyService myService)
    {
        _myService = myService;
    }
}
```

### 4. Configuration
Provide meaningful configuration methods:

```csharp
[PluginConfiguration("DatabasePath")]
public string GetDatabasePath()
{
    return Path.Combine(AppContext.BaseDirectory, "mydata.db");
}

[PluginConfiguration("IsEnabled")]
public bool IsEnabled()
{
    return true;
}
```

## Deployment

1. Build your plugin project
2. Copy the output DLL to the `Plugins` folder in the main application
3. Restart the application
4. Your plugin will be automatically discovered and loaded

## Troubleshooting

### Plugin Not Loading
- Check that the DLL is in the correct Plugins folder
- Ensure the class has the `[Plugin]` attribute
- Check console output for error messages
- Verify the plugin targets the correct .NET version

### Tab Not Appearing
- Ensure the `[PluginTabProvider]` method returns a valid control
- Check that the method doesn't throw exceptions
- Verify the plugin loaded successfully

### Configuration Not Working
- Check that configuration methods have the `[PluginConfiguration]` attribute
- Ensure the key name is correct
- Verify the method signature is correct

## Example Plugin Projects

See the existing plugin projects for examples:
- `Mis.Agent.Barcode` - Barcode scanning functionality
- `Mis.Agent.Print` - Printing and notifications
- `Mis.Agent.Port` - Port configuration

## Migration from Old System

To migrate existing plugins:

1. Remove references to `Mis.Shared.Interface`
2. Replace `IPlugin` interface with `[Plugin]` attribute
3. Add appropriate method attributes
4. Update method signatures as needed
5. Remove unused methods (like empty `Execute()` methods)
