﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <OutputPath>..\..\Mis.Agent\bin\Debug\net9.0-windows\Plugins\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>


  <ItemGroup>
    <!--<PackageReference Include="Microsoft.AspNetCore" Version="2.2.0" />-->
    <PackageReference Include="Microsoft.AspNetCore.Owin" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.0" />
    <PackageReference Include="Volo.Abp.Core" Version="7.3.2" />
    <PackageReference Include="System.IO.Ports" Version="9.0.6" />
    <PackageReference Include="System.Management" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>


  <ItemGroup>
    <Reference Include="Interop.WIA">
      <HintPath>WIA\Interop.WIA.dll</HintPath>
    </Reference>
  </ItemGroup>


  <ItemGroup>
    <Compile Update="Resources\BarcodeResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>BarcodeResources.resx</DependentUpon>
    </Compile>
  </ItemGroup>


  <ItemGroup>
    <EmbeddedResource Update="Resources\BarcodeResources.ar-SA.resx">
      <CustomToolNamespace>Mis.Agent.Barcode.Resources</CustomToolNamespace>
      <Generator>ResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\BarcodeResources.resx">
      <CustomToolNamespace>Mis.Agent.Barcode.Resources</CustomToolNamespace>
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>BarcodeResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
</Project>