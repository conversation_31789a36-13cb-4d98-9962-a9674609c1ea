using System.Globalization;
using System.Resources;

namespace Mis.Agent.Localization
{
    public interface ILocalizationManager
    {
        /// <summary>
        /// Gets the current culture
        /// </summary>
        CultureInfo CurrentCulture { get; }

        /// <summary>
        /// Sets the current culture and updates UI
        /// </summary>
        /// <param name="cultureName">Culture name (e.g., "en-US", "ar-SA")</param>
        void SetCulture(string cultureName);

        /// <summary>
        /// Gets a localized string by key
        /// </summary>
        /// <param name="key">Resource key</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>Localized string</returns>
        string GetString(string key, string defaultValue = null);

        /// <summary>
        /// Gets a localized string with parameters
        /// </summary>
        /// <param name="key">Resource key</param>
        /// <param name="args">Format arguments</param>
        /// <returns>Formatted localized string</returns>
        string GetString(string key, params object[] args);

        /// <summary>
        /// Checks if the current culture is RTL
        /// </summary>
        bool IsRightToLeft { get; }

        /// <summary>
        /// Event fired when culture changes
        /// </summary>
        event EventHandler<CultureChangedEventArgs> CultureChanged;

        /// <summary>
        /// Saves the current language preference
        /// </summary>
        void SaveLanguagePreference();

        /// <summary>
        /// Loads the saved language preference
        /// </summary>
        void LoadLanguagePreference();

        /// <summary>
        /// Registers a resource manager with a key
        /// </summary>
        /// <param name="key">Resource manager key</param>
        /// <param name="resourceManager">Resource manager instance</param>
        void RegisterResourceManager(string key, ResourceManager resourceManager);
    }

    public class CultureChangedEventArgs : EventArgs
    {
        public CultureInfo OldCulture { get; set; }
        public CultureInfo NewCulture { get; set; }
        public bool IsRightToLeft { get; set; }
    }
}
