﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <OutputPath>..\..\Mis.Agent\bin\Debug\net9.0-windows\Plugins\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HtmlRenderer.PdfSharp" Version="*******" />
    <PackageReference Include="Microsoft.AspNetCore.Owin" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="MigraDocCore.Rendering" Version="1.3.67" />
    <PackageReference Include="PdfSharpCore" Version="1.3.67" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.6" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="9.0.6" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="System.Security.Cryptography.Pkcs" Version="9.0.6" />
    <PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.0.6" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>


  <ItemGroup>
    <Compile Update="Resources\PortResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>PortResources.resx</DependentUpon>
    </Compile>
  </ItemGroup>


  <ItemGroup>
    <EmbeddedResource Update="Resources\PortResources.ar-SA.resx">
      <CustomToolNamespace>Mis.Agent.Port.Resources</CustomToolNamespace>
      <Generator>ResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\PortResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>PortResources.Designer.cs</LastGenOutput>
      <CustomToolNamespace>Mis.Agent.Port.Resources</CustomToolNamespace>
    </EmbeddedResource>
  </ItemGroup>
</Project>