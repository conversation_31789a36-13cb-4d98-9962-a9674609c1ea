﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Shared.Interface
{
    public class EntitiesAppsettings
    {
        public NotificationSettings NotificationSettings { get; set; }
    }

    public class NotificationSettings
    {
        public bool? EnabeledNotification { get; set; }
        public NotificationCleanupSettings NotificationCleanup { get; set; }
    }

    public class NotificationCleanupSettings
    {
        public bool Enabled { get; set; }
        public int? MaxRecords { get; set; }
        public int? KeepDays { get; set; }
    }
}

