using Mis.Shared.Interface;
using System.Diagnostics;
using Mis.Agent.Barcode.Resources;

namespace Mis.Agent.Barcode
{
    public partial class BarcodeForm : Form, ICultureChangeNotifiable
    {
        private readonly IBarcodeAppService _barcodeAppService;
        private ToolTip toolTip1 = new ToolTip();


        public BarcodeForm(IBarcodeAppService barcodeAppService)
        {
            _barcodeAppService = barcodeAppService;
            InitializeComponent();
            InitializeLocalization();
            FillTextFromConfiguration();

        }

        private void FillTextFromConfiguration()
        {
            var barcodeUrl = _barcodeAppService.GetBarcodeBaseUrl();

            if (Uri.TryCreate(barcodeUrl, UriKind.Absolute, out Uri uri))
            {
                // ? Set protocol ("http" or "https")
                barcodeProtocolField.SelectedItem = uri.Scheme;

                // ? Set host (IP or domain)
                barcodeIpField.Text = uri.Host;

                // ? Set port
                barcodePortField.Text = uri.Port.ToString();

                // ? Set full URL in read-only textbox
                barcodeUrlTextBox.Text = uri.ToString();
                barcodeUrlTextBox.BackColor = Color.White;
            }
            else
            {
                // ? Show fallback if barcodeUrl is malformed
                barcodeUrlTextBox.Text = "";
                barcodeUrlTextBox.BackColor = Color.LightPink;
                toolTip1.SetToolTip(barcodeUrlTextBox, "������ ������ �� ��������� ��� ����");
            }
        }


        private void InitializeLocalization()
        {
            // Register for culture change notifications
            NotificationManager.RegisterForCultureChange(this);

            // Apply initial localization
            ApplyLocalization();
        }

        public void OnCultureChanged()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged()));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = BarcodeResources.BarcodeFormTitle;

            // Update tab text
            BarcodeTab.Text = BarcodeResources.BarcodeTabText;

            // Update labels
            label1.Text = BarcodeResources.BarcodePortLabel;
            label2.Text = BarcodeResources.BarcodeIpLabel;
            label3.Text = BarcodeResources.BarcodeProtocolLabel;
            label4.Text = BarcodeResources.BarcodeUrlLabel;
            label5.Text = BarcodeResources.ComPortLabel;
            label6.Text = BarcodeResources.AvailablePortsLabel;

            // Update buttons
            button3.Text = BarcodeResources.TestConnectionButton;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Unregister from culture change notifications
            NotificationManager.UnregisterFromCultureChange(this);
            base.OnFormClosing(e);
        }




        private void UpdateBarcodeUrl()
        {
            string ip = barcodeIpField.Text.Trim();
            string portText = barcodePortField.Text.Trim();
            string protocol = barcodeProtocolField.SelectedItem?.ToString()?.ToLower();

            bool isPortValid = int.TryParse(portText, out int port) && port > 0 && port <= 65535;

            if (string.IsNullOrWhiteSpace(protocol))
            {
                barcodeUrlTextBox.Text = "";
                barcodeUrlTextBox.BackColor = Color.LightPink;
                toolTip1.SetToolTip(barcodeUrlTextBox, "Please select protocol: http or https.");
                return;
            }

            if (!string.IsNullOrWhiteSpace(ip) && isPortValid)
            {
                string finalUrl = $"{protocol}://{ip}:{port}/chatHub";

                if (Uri.TryCreate(finalUrl, UriKind.Absolute, out Uri uri))
                {
                    barcodeUrlTextBox.Text = uri.ToString();
                    barcodeUrlTextBox.BackColor = Color.White;
                }
                else
                {
                    barcodeUrlTextBox.BackColor = Color.LightYellow;
                    toolTip1.SetToolTip(barcodeUrlTextBox, "Invalid URL format. Check IP and port.");
                }
            }
            else
            {
                barcodeUrlTextBox.Text = "";
                barcodeUrlTextBox.BackColor = Color.LightPink;
                toolTip1.SetToolTip(barcodeUrlTextBox, "Enter valid IP and port.");
            }
        }

        private void barcodeIpField_TextChanged(object sender, EventArgs e)
        {
            UpdateBarcodeUrl();
        }

        private void barcodePortField_TextChanged(object sender, EventArgs e)
        {
            UpdateBarcodeUrl();
        }

        private void barcodeProtocolField_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateBarcodeUrl();
        }
    }
}
