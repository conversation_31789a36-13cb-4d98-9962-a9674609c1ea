﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Mis.Agent.Port.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class PortResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal PortResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Mis.Agent.Port.Resources.PortResources", typeof(PortResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent Url:.
        /// </summary>
        internal static string AgentUrlLabel {
            get {
                return ResourceManager.GetString("AgentUrlLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save configuration. Please try again..
        /// </summary>
        internal static string ConfigurationFailedMessage {
            get {
                return ResourceManager.GetString("ConfigurationFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration saved successfully!.
        /// </summary>
        internal static string ConfigurationSavedMessage {
            get {
                return ResourceManager.GetString("ConfigurationSavedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection test failed. Please check your settings..
        /// </summary>
        internal static string ConnectionFailedMessage {
            get {
                return ResourceManager.GetString("ConnectionFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection test successful!.
        /// </summary>
        internal static string ConnectionSuccessMessage {
            get {
                return ResourceManager.GetString("ConnectionSuccessMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to send manual data..
        /// </summary>
        internal static string DataSendFailedMessage {
            get {
                return ResourceManager.GetString("DataSendFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual data sent successfully!.
        /// </summary>
        internal static string DataSentMessage {
            get {
                return ResourceManager.GetString("DataSentMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid URL..
        /// </summary>
        internal static string InvalidUrlMessage {
            get {
                return ResourceManager.GetString("InvalidUrlMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a COM port..
        /// </summary>
        internal static string NoPortSelectedMessage {
            get {
                return ResourceManager.GetString("NoPortSelectedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port Configuration.
        /// </summary>
        internal static string PortFormTitle {
            get {
                return ResourceManager.GetString("PortFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port Settings.
        /// </summary>
        internal static string PortTabText {
            get {
                return ResourceManager.GetString("PortTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Port Configuration.
        /// </summary>
        internal static string SavePortConfigButton {
            get {
                return ResourceManager.GetString("SavePortConfigButton", resourceCulture);
            }
        }

        internal static string AgentPortLabel
        {
            get
            {
                return ResourceManager.GetString("AgentPortLabel", resourceCulture);
            }
        }
        internal static string AgentIpLabel
        {
            get
            {
                return ResourceManager.GetString("AgentIpLabel", resourceCulture);
            }
        }

        internal static string AgentProtocolLabel
        {
            get
            {
                return ResourceManager.GetString("AgentProtocolLabel", resourceCulture);
            }
        }

      

    }
}
