﻿using Mis.Shared.Interface;

namespace Mis.Agent.Print
{
    public interface INotificationAppService
    {
        bool DatabaseFileExists();
        string GetDatabasePath();
        bool TableExists(string tableName);
        Task ClearAllNotificationsAsync();
        void ShowNotification(string title, string text);
        Task<NotificationSettings> GetNotificationSettingsAsync();
        Task PerformCleanupAsync();
        Task<IEnumerable<TransactionDto>> GetAllNotificationsAsync();
        Task SaveNotificationAsync(TransactionDto input);
        Task UpdatePrintStatusAsync(Guid transactionId, bool isPrinted);

    }
}