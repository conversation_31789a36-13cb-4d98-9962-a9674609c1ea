﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Mis.Agent;
using Mis.Agent.Localization;
using Mis.Agent.PluginSystem;

using Mis.Shared.Interface;
using System;
using System.Configuration;
using System.Data;
using System.IO.Ports;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using TransactionDto = Mis.Shared.Interface.TransactionDto;

namespace Mis.Agent.ApplicationsContext
{
    public class AgentStarter : ApplicationContext
    {
        private IHost _webHost;
        private NotifyIcon trayIcon;
        private TransactionDto? _transactionDto;
        private AgentForm? _agentForm;
        IServiceProvider _serviceProvider;
        private ILogger<AgentStarter> _logger;

        bool _notificationsEnabled;
        private readonly IConfiguration _configuration;

        public AgentStarter(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = _serviceProvider.GetService<ILogger<AgentStarter>>();
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _configuration = new ConfigurationBuilder()
             .SetBasePath(AppContext.BaseDirectory)
             .AddJsonFile("appsettings.json", optional: true)
             .Build();
            // Subscribe to plugin events via the shared NotificationManager (no plugin references)
            NotificationManager.NotificationDataUpdated += OnNotificationUpdated;
            NotificationManager.BarcodeImageCaptured += OnImageCaptured;
            NotificationManager.ScannerImageCaptured += OnImageCaptured;

            InitializeTrayIcon();
            GetNotificationEnabledSetting();
        }
        public void GetNotificationEnabledSetting()
        {
            var section = _configuration.GetSection("NotificationSettings");
            _notificationsEnabled = section.GetValue<bool?>("EnabeledNotification") ?? false;
        }
        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public void ShowNotification(string title, string text)
        {
            // Use centralized NotificationManager for consistent localization
            NotificationManager.SetNotificationEnabled(_notificationsEnabled);
            NotificationManager.ShowNotification(title, text);
        }



        private void InitializeTrayIcon()
        {
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.AddRange(new ToolStripItem[] {
            new ToolStripMenuItem("Show Tabs Form", null, ShowTabsForm),
            new ToolStripMenuItem("Exit", null, Exit)
        });

            trayIcon = new NotifyIcon
            {
                Icon = Icon.ExtractAssociatedIcon(Application.ExecutablePath),
                ContextMenuStrip = contextMenu,
                Visible = true,
                Text = "Agent Application"
            };

            trayIcon.MouseClick += TrayIcon_MouseClick;
        }



        private async void TrayIcon_MouseClick(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                await ShowOrRefreshTabsFormAsync();
            }
        }

        private async void ShowTabsForm(object? sender, EventArgs e)
        {
            try
            {
                bool result = await ShowOrRefreshTabsFormAsync();
                ShowNotification(AgentResources.AgentNotification, result ? AgentResources.TabsFormIsAlreadyOpened : AgentResources.TabsFormOperationFailed);
            }
            catch (Exception ex)
            {
                ShowNotification(AgentResources.Error, AgentResources.TabsFormOperationFailed);
                _logger.LogError(ex.Message);
            }
        }

        public async Task<bool> ShowOrRefreshTabsFormAsync()
        {
            try
            {
                if (_agentForm == null || _agentForm.IsDisposed)
                {
                    return await RunTabsFormAsync(_transactionDto);
                }
                else
                {
                    if (_agentForm.InvokeRequired)
                    {
                        _agentForm.Invoke(new Action(() =>
                        {
                            _agentForm.BringToFront();
                            _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                        }));
                    }
                    else
                    {
                        _agentForm.BringToFront();
                        _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($" An error occurred in ShowOrRefreshTabsFormAsync: {ex.Message}");

                return true;
            }

        }

        private async Task<bool> RunTabsFormAsync(TransactionDto? transactionDto)
        {
            try
            {
                var taskCompletionSource = new TaskCompletionSource<bool>();

                var transactionFormThread = new Thread(() =>
                {
                    try
                    {
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);
                        _agentForm = new AgentForm(_serviceProvider)
                        {
                            StartPosition = FormStartPosition.CenterScreen
                        };
                        Application.Run(_agentForm);
                        taskCompletionSource.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        taskCompletionSource.SetException(ex);
                    }
                });

                transactionFormThread.SetApartmentState(ApartmentState.STA);
                transactionFormThread.Start();

                return await taskCompletionSource.Task;
            }
            catch (Exception ex)
            {
                _logger.LogError($" An error occurred in RunTabsFormAsync: {ex.Message}");
                return false;
            }

        }


        private void Exit(object? sender, EventArgs e)
        {
            try
            {
                trayIcon.Visible = false;
                Application.Exit();
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception in Exit method: {ex.Message}");
            }
        }
        private void OnImageCaptured(Image capturedImage)
        {
            try
            {
                // Access the ScannerTab by its name
                var scannerTab = _agentForm?.tabControl1.TabPages["ScannerTab"] as TabPage;
                if (scannerTab != null)
                {
                    var pictureBox = scannerTab.Controls["pictureScanned"] as PictureBox;
                    if (pictureBox != null)
                    {
                        pictureBox.Image = capturedImage;
                    }
                    else
                    {
                        ShowNotification(AgentResources.Error, AgentResources.PictureBoxNotFound);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                ShowNotification(AgentResources.Error, AgentResources.ErrorInCaptureImageInAgentStarter);
                _logger.LogError($"Error occurred: {ex.Message}", "Error");
            }
        }
        private void OnNotificationUpdated()
        {
            RefreshDataGridView();
        }
        /// <summary>
        /// Refreshes the notifications DataGridView using the professional shared method
        /// </summary>
        private async void RefreshDataGridView()
        {
            try
            {
                if (_agentForm?.tabControl1 != null)
                {
                    await AgentForm.RefreshNotificationsDataGridViewAsync(_agentForm.tabControl1);
                }
                else
                {
                    _logger.LogWarning("AgentForm or TabControl not available for refresh.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in AgentStarter.RefreshDataGridView: {ex.Message}");
               
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from events to prevent memory leaks
                NotificationManager.NotificationDataUpdated -= OnNotificationUpdated;
                NotificationManager.BarcodeImageCaptured -= OnImageCaptured;
                NotificationManager.ScannerImageCaptured -= OnImageCaptured;

                trayIcon?.Dispose();
                _agentForm?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}